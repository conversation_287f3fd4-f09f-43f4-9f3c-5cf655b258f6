import sys
import pyotp
import time
import re
import base64
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar)
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QClipboard
from datetime import datetime

class TOTPGenerator(QWidget):
    def __init__(self):
        super().__init__()
        self.totp = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_codes)
        self.current_code = ""
        self.next_code = ""
        self.initUI()

    def normalize_secret_key(self, secret):
        """
        标准化和修复TOTP密钥格式
        """
        # 移除所有空格和常见分隔符
        secret = re.sub(r'[\s\-_]', '', secret.upper())
        
        # 如果密钥以"Key "开头，移除它
        if secret.startswith('KEY'):
            secret = secret[3:]
        
        # 确保密钥只包含有效的Base32字符
        # Base32字符集: A-Z, 2-7
        secret = re.sub(r'[^A-Z2-7]', '', secret)
        
        # 修复Base32 padding
        # Base32需要长度是8的倍数，用'='填充
        while len(secret) % 8 != 0:
            secret += '='
            
        return secret

    def validate_secret_key(self, secret):
        """
        验证密钥格式是否正确
        """
        try:
            normalized = self.normalize_secret_key(secret)
            if len(normalized) < 16:  # 最小长度检查
                return False, "密钥太短 (最少16个字符)"
            
            # 尝试解码Base32
            base64.b32decode(normalized)
            return True, normalized
        except Exception as e:
            return False, f"无效的Base32格式: {str(e)}"

    def initUI(self):
        # 窗口设置
        self.setWindowTitle('双因子认证码生成器')
        self.setGeometry(100, 100, 500, 350)

        # 布局
        layout = QVBoxLayout()

        # 密钥输入
        self.secretLabel = QLabel('请输入密钥:')
        self.secretInput = QLineEdit(self)
        self.secretInput.setPlaceholderText("例如: JBSWY3DPEHPK3PXP 或 Key sdvb1234...")
        layout.addWidget(self.secretLabel)
        layout.addWidget(self.secretInput)

        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 生成按钮
        self.generateButton = QPushButton('生成验证码')
        self.generateButton.clicked.connect(self.start_generation)
        button_layout.addWidget(self.generateButton)
        
        # 停止按钮
        self.stopButton = QPushButton('停止自动更新')
        self.stopButton.clicked.connect(self.stop_generation)
        self.stopButton.setEnabled(False)
        button_layout.addWidget(self.stopButton)
        
        layout.addLayout(button_layout)

        # 进度条显示剩余时间
        self.progressLabel = QLabel('当前周期剩余时间:')
        self.progressBar = QProgressBar()
        self.progressBar.setMaximum(30)  # TOTP周期为30秒
        layout.addWidget(self.progressLabel)
        layout.addWidget(self.progressBar)

        # 验证码显示区域
        codes_layout = QVBoxLayout()

        # 当前验证码
        self.currentCodeLabel = QLabel('当前验证码:')
        self.currentCodeButton = QPushButton('------')
        self.currentCodeButton.setStyleSheet("""
            QPushButton {
                font-family: 'Courier New', monospace;
                font-size: 18pt;
                font-weight: bold;
                padding: 10px;
                background-color: #e8f5e8;
                border: 2px solid #4CAF50;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #d4edda;
            }
            QPushButton:pressed {
                background-color: #c3e6cb;
            }
        """)
        self.currentCodeButton.clicked.connect(self.copy_current_code)
        self.currentCodeButton.setEnabled(False)

        codes_layout.addWidget(self.currentCodeLabel)
        codes_layout.addWidget(self.currentCodeButton)

        # 下一个验证码
        self.nextCodeLabel = QLabel('下一个验证码:')
        self.nextCodeButton = QPushButton('------')
        self.nextCodeButton.setStyleSheet("""
            QPushButton {
                font-family: 'Courier New', monospace;
                font-size: 16pt;
                padding: 8px;
                background-color: #f8f9fa;
                border: 2px solid #6c757d;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        self.nextCodeButton.clicked.connect(self.copy_next_code)
        self.nextCodeButton.setEnabled(False)

        codes_layout.addWidget(self.nextCodeLabel)
        codes_layout.addWidget(self.nextCodeButton)

        layout.addLayout(codes_layout)

        # 状态显示区域
        self.statusArea = QTextEdit(self)
        self.statusArea.setReadOnly(True)
        self.statusArea.setMaximumHeight(100)
        self.statusArea.setStyleSheet("font-family: 'Microsoft YaHei', sans-serif; font-size: 10pt;")
        layout.addWidget(self.statusArea)

        # 设置主布局
        self.setLayout(layout)

    def copy_current_code(self):
        """复制当前验证码到剪贴板"""
        if self.current_code:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.current_code)
            self.show_status(f"已复制当前验证码: {self.current_code}")

    def copy_next_code(self):
        """复制下一个验证码到剪贴板"""
        if self.next_code:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.next_code)
            self.show_status(f"已复制下一个验证码: {self.next_code}")

    def show_status(self, message):
        """显示状态信息"""
        current_time = datetime.now().strftime("%H:%M:%S")
        status_msg = f"[{current_time}] {message}"
        self.statusArea.append(status_msg)

    def start_generation(self):
        secret = self.secretInput.text().strip()
        if not secret:
            self.statusArea.setText("请输入密钥!")
            return

        # 验证和标准化密钥
        is_valid, result = self.validate_secret_key(secret)
        if not is_valid:
            error_msg = f"错误: 无效的密钥格式\n\n"
            error_msg += f"详细信息: {result}\n\n"
            error_msg += "支持的格式:\n"
            error_msg += "• 标准Base32格式: JBSWY3DPEHPK3PXP\n"
            error_msg += "• 带Key前缀: Key JBSWY3DPEHPK3PXP\n"
            error_msg += "• 带空格或连字符: JBSW Y3DP-EHPK-3PXP\n\n"
            error_msg += "提示:\n"
            error_msg += "• 请移除所有空格和特殊字符\n"
            error_msg += "• 只能使用字母A-Z和数字2-7\n"
            error_msg += "• 最小长度: 16个字符"
            self.statusArea.setText(error_msg)
            return

        try:
            # 使用标准化的密钥初始化 TOTP 对象
            normalized_secret = result
            self.totp = pyotp.TOTP(normalized_secret)

            # 测试密钥是否有效
            _ = self.totp.now()  # 测试生成代码

            # 开始定时更新
            self.timer.start(1000)  # 每秒更新一次
            self.generateButton.setEnabled(False)
            self.stopButton.setEnabled(True)
            self.secretInput.setEnabled(False)
            self.currentCodeButton.setEnabled(True)
            self.nextCodeButton.setEnabled(True)

            # 清空状态区域并显示开始信息
            self.statusArea.clear()
            self.show_status("开始生成验证码，点击验证码可复制到剪贴板")

            # 立即更新一次
            self.update_codes()

        except Exception as e:
            self.statusArea.setText(f"错误: {str(e)}\n\n请检查您的密钥格式是否正确。")

    def stop_generation(self):
        self.timer.stop()
        self.generateButton.setEnabled(True)
        self.stopButton.setEnabled(False)
        self.secretInput.setEnabled(True)
        self.currentCodeButton.setEnabled(False)
        self.nextCodeButton.setEnabled(False)
        self.currentCodeButton.setText('------')
        self.nextCodeButton.setText('------')
        self.progressBar.setValue(0)
        self.show_status("已停止生成验证码")

    def update_codes(self):
        if not self.totp:
            return

        try:
            # 获取当前时间戳
            current_time = int(time.time())
            
            # 计算当前30秒周期内的位置
            time_in_period = current_time % 30
            remaining_time = 30 - time_in_period
            
            # 更新进度条
            self.progressBar.setValue(remaining_time)
            
            # 当前代码
            self.current_code = self.totp.now()

            # 下一个代码（通过计算下一个30秒周期的时间戳）
            next_period_time = current_time + remaining_time
            self.next_code = self.totp.at(next_period_time)

            # 更新按钮显示
            self.currentCodeButton.setText(self.current_code)
            self.nextCodeButton.setText(self.next_code)

            # 更新标签显示剩余时间
            self.currentCodeLabel.setText(f"当前验证码 (有效期剩余 {remaining_time} 秒):")
            self.nextCodeLabel.setText(f"下一个验证码 (将在 {remaining_time} 秒后生效):")

        except Exception as e:
            self.show_status(f"更新验证码时出错: {str(e)}")
            self.stop_generation()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TOTPGenerator()
    window.show()
    sys.exit(app.exec_())
