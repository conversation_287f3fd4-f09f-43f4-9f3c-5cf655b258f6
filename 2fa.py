import sys
import pyotp
import time
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar)
from PyQt5.QtCore import QTimer
from datetime import datetime

class TOTPGenerator(QWidget):
    def __init__(self):
        super().__init__()
        self.totp = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_codes)
        self.initUI()

    def initUI(self):
        # 窗口设置
        self.setWindowTitle('2FA Code Generator - Enhanced')
        self.setGeometry(100, 100, 500, 350)

        # 布局
        layout = QVBoxLayout()

        # 密钥输入
        self.secretLabel = QLabel('Enter Secret Key (e.g., Key sdvb...):')
        self.secretInput = QLineEdit(self)
        self.secretInput.setPlaceholderText("Enter your TOTP secret key here...")
        layout.addWidget(self.secretLabel)
        layout.addWidget(self.secretInput)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 生成按钮
        self.generateButton = QPushButton('Generate Codes')
        self.generateButton.clicked.connect(self.start_generation)
        button_layout.addWidget(self.generateButton)

        # 停止按钮
        self.stopButton = QPushButton('Stop Auto-Update')
        self.stopButton.clicked.connect(self.stop_generation)
        self.stopButton.setEnabled(False)
        button_layout.addWidget(self.stopButton)

        layout.addLayout(button_layout)

        # 进度条显示剩余时间
        self.progressLabel = QLabel('Time remaining in current period:')
        self.progressBar = QProgressBar()
        self.progressBar.setMaximum(30)  # TOTP周期为30秒
        layout.addWidget(self.progressLabel)
        layout.addWidget(self.progressBar)

        # 显示结果
        self.resultArea = QTextEdit(self)
        self.resultArea.setReadOnly(True)
        self.resultArea.setStyleSheet("font-family: 'Courier New', monospace; font-size: 12pt;")
        layout.addWidget(self.resultArea)

        # 设置主布局
        self.setLayout(layout)

    def start_generation(self):
        secret = self.secretInput.text().strip()
        if not secret:
            self.resultArea.setText("Please enter a secret key!")
            return

        try:
            # 初始化 TOTP 对象
            self.totp = pyotp.TOTP(secret)

            # 测试密钥是否有效
            _ = self.totp.now()  # 测试生成代码

            # 开始定时更新
            self.timer.start(1000)  # 每秒更新一次
            self.generateButton.setEnabled(False)
            self.stopButton.setEnabled(True)
            self.secretInput.setEnabled(False)

            # 立即更新一次
            self.update_codes()

        except Exception as e:
            self.resultArea.setText(f"Error: {str(e)}\nPlease check your secret key format.")

    def stop_generation(self):
        self.timer.stop()
        self.generateButton.setEnabled(True)
        self.stopButton.setEnabled(False)
        self.secretInput.setEnabled(True)
        self.progressBar.setValue(0)

    def update_codes(self):
        if not self.totp:
            return

        try:
            # 获取当前时间戳
            current_time = int(time.time())

            # 计算当前30秒周期内的位置
            time_in_period = current_time % 30
            remaining_time = 30 - time_in_period

            # 更新进度条
            self.progressBar.setValue(remaining_time)

            # 当前代码
            current_code = self.totp.now()

            # 下一个代码（通过计算下一个30秒周期的时间戳）
            next_period_time = current_time + remaining_time
            next_code = self.totp.at(next_period_time)

            # 格式化显示结果
            current_time_str = datetime.now().strftime("%H:%M:%S")
            result = f"=== 2FA Codes Generated at {current_time_str} ===\n\n"
            result += f"🔑 Secret Key: {self.secretInput.text()[:8]}...\n\n"
            result += f"📱 Current Code:  {current_code}\n"
            result += f"⏭️  Next Code:     {next_code}\n\n"
            result += f"⏰ Time remaining: {remaining_time} seconds\n"
            result += f"🔄 Auto-updating every second...\n\n"
            result += "Note: Current code is valid for this 30-second period.\n"
            result += "Next code will be valid in the following period."

            self.resultArea.setText(result)

        except Exception as e:
            self.resultArea.setText(f"Error updating codes: {str(e)}")
            self.stop_generation()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TOTPGenerator()
    window.show()
    sys.exit(app.exec_())