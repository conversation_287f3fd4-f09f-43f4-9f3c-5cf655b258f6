import sys
import pyotp
import time
import re
import base64
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar)
from PyQt5.QtCore import QTimer
from datetime import datetime

class TOTPGenerator(QWidget):
    def __init__(self):
        super().__init__()
        self.totp = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_codes)
        self.initUI()

    def normalize_secret_key(self, secret):
        """
        标准化和修复TOTP密钥格式
        """
        # 移除所有空格和常见分隔符
        secret = re.sub(r'[\s\-_]', '', secret.upper())
        
        # 如果密钥以"Key "开头，移除它
        if secret.startswith('KEY'):
            secret = secret[3:]
        
        # 确保密钥只包含有效的Base32字符
        # Base32字符集: A-Z, 2-7
        secret = re.sub(r'[^A-Z2-7]', '', secret)
        
        # 修复Base32 padding
        # Base32需要长度是8的倍数，用'='填充
        while len(secret) % 8 != 0:
            secret += '='
            
        return secret

    def validate_secret_key(self, secret):
        """
        验证密钥格式是否正确
        """
        try:
            normalized = self.normalize_secret_key(secret)
            if len(normalized) < 16:  # 最小长度检查
                return False, "密钥太短 (最少16个字符)"
            
            # 尝试解码Base32
            base64.b32decode(normalized)
            return True, normalized
        except Exception as e:
            return False, f"无效的Base32格式: {str(e)}"

    def initUI(self):
        # 窗口设置
        self.setWindowTitle('双因子认证码生成器')
        self.setGeometry(100, 100, 500, 350)

        # 布局
        layout = QVBoxLayout()

        # 密钥输入
        self.secretLabel = QLabel('请输入密钥:')
        self.secretInput = QLineEdit(self)
        self.secretInput.setPlaceholderText("例如: JBSWY3DPEHPK3PXP 或 Key sdvb1234...")
        layout.addWidget(self.secretLabel)
        layout.addWidget(self.secretInput)

        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 生成按钮
        self.generateButton = QPushButton('生成验证码')
        self.generateButton.clicked.connect(self.start_generation)
        button_layout.addWidget(self.generateButton)
        
        # 停止按钮
        self.stopButton = QPushButton('停止自动更新')
        self.stopButton.clicked.connect(self.stop_generation)
        self.stopButton.setEnabled(False)
        button_layout.addWidget(self.stopButton)
        
        layout.addLayout(button_layout)

        # 进度条显示剩余时间
        self.progressLabel = QLabel('当前周期剩余时间:')
        self.progressBar = QProgressBar()
        self.progressBar.setMaximum(30)  # TOTP周期为30秒
        layout.addWidget(self.progressLabel)
        layout.addWidget(self.progressBar)

        # 显示结果
        self.resultArea = QTextEdit(self)
        self.resultArea.setReadOnly(True)
        self.resultArea.setStyleSheet("font-family: 'Courier New', monospace; font-size: 12pt;")
        layout.addWidget(self.resultArea)

        # 设置主布局
        self.setLayout(layout)

    def start_generation(self):
        secret = self.secretInput.text().strip()
        if not secret:
            self.resultArea.setText("请输入密钥!")
            return

        # 验证和标准化密钥
        is_valid, result = self.validate_secret_key(secret)
        if not is_valid:
            error_msg = f"错误: 无效的密钥格式\n\n"
            error_msg += f"详细信息: {result}\n\n"
            error_msg += "支持的格式:\n"
            error_msg += "• 标准Base32格式: JBSWY3DPEHPK3PXP\n"
            error_msg += "• 带Key前缀: Key JBSWY3DPEHPK3PXP\n"
            error_msg += "• 带空格或连字符: JBSW Y3DP-EHPK-3PXP\n\n"
            error_msg += "提示:\n"
            error_msg += "• 请移除所有空格和特殊字符\n"
            error_msg += "• 只能使用字母A-Z和数字2-7\n"
            error_msg += "• 最小长度: 16个字符"
            self.resultArea.setText(error_msg)
            return

        try:
            # 使用标准化的密钥初始化 TOTP 对象
            normalized_secret = result
            self.totp = pyotp.TOTP(normalized_secret)

            # 测试密钥是否有效
            _ = self.totp.now()  # 测试生成代码

            # 开始定时更新
            self.timer.start(1000)  # 每秒更新一次
            self.generateButton.setEnabled(False)
            self.stopButton.setEnabled(True)
            self.secretInput.setEnabled(False)

            # 立即更新一次
            self.update_codes()

        except Exception as e:
            self.resultArea.setText(f"错误: {str(e)}\n\n请检查您的密钥格式是否正确。")

    def stop_generation(self):
        self.timer.stop()
        self.generateButton.setEnabled(True)
        self.stopButton.setEnabled(False)
        self.secretInput.setEnabled(True)
        self.progressBar.setValue(0)

    def update_codes(self):
        if not self.totp:
            return

        try:
            # 获取当前时间戳
            current_time = int(time.time())
            
            # 计算当前30秒周期内的位置
            time_in_period = current_time % 30
            remaining_time = 30 - time_in_period
            
            # 更新进度条
            self.progressBar.setValue(remaining_time)
            
            # 当前代码
            current_code = self.totp.now()
            
            # 下一个代码（通过计算下一个30秒周期的时间戳）
            next_period_time = current_time + remaining_time
            next_code = self.totp.at(next_period_time)
            
            # 格式化显示结果
            current_time_str = datetime.now().strftime("%H:%M:%S")
            result = f"双因子验证码生成成功 - {current_time_str}\n"
            result += f"{'='*50}\n\n"
            
            # 显示当前和下一个代码
            result += f"当前验证码 (有效期剩余 {remaining_time} 秒):\n"
            result += f"    {current_code}\n\n"
            result += f"下一个验证码 (将在 {remaining_time} 秒后生效):\n"
            result += f"    {next_code}\n\n"
            
            result += f"{'='*50}\n"
            result += f"密钥: {self.secretInput.text()[:12]}...\n"
            result += f"剩余时间: {remaining_time} 秒\n"
            result += f"自动刷新: 开启\n\n"
            result += "提示: 请使用当前验证码进行身份验证。\n"
            result += "      下一个验证码将在下个周期生效。"
            
            self.resultArea.setText(result)

        except Exception as e:
            self.resultArea.setText(f"更新验证码时出错: {str(e)}")
            self.stop_generation()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TOTPGenerator()
    window.show()
    sys.exit(app.exec_())
