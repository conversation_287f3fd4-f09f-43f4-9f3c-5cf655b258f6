import sys
import pyotp
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QPushButton, QTextEdit)
from datetime import datetime, timedelta

class TOTPGenerator(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        # 窗口设置
        self.setWindowTitle('2FA Code Generator')
        self.setGeometry(100, 100, 400, 200)

        # 布局
        layout = QVBoxLayout()

        # 密钥输入
        self.secretLabel = QLabel('Enter Secret Key (e.g., Key sdvb...):')
        self.secretInput = QLineEdit(self)
        layout.addWidget(self.secretLabel)
        layout.addWidget(self.secretInput)

        # 生成按钮
        self.generateButton = QPushButton('Generate Codes')
        self.generateButton.clicked.connect(self.generate_codes)
        layout.addWidget(self.generateButton)

        # 显示结果
        self.resultArea = QTextEdit(self)
        self.resultArea.setReadOnly(True)
        layout.addWidget(self.resultArea)

        # 设置主布局
        self.setLayout(layout)

    def generate_codes(self):
        secret = self.secretInput.text().strip()
        if not secret:
            self.resultArea.setText("Please enter a secret key!")
            return

        try:
            # 初始化 TOTP 对象
            totp = pyotp.TOTP(secret)

            # 当前时间代码
            current_code = totp.now()

            # 下一个30秒周期的代码
            next_time = datetime.now() + timedelta(seconds=30)
            totp.time = next_time.timestamp()
            next_code = totp.now()

            # 显示结果
            result = f"Current Code (now): {current_code}\n"
            result += f"Next Code (+30s): {next_code}\n"
            self.resultArea.setText(result)

        except Exception as e:
            self.resultArea.setText(f"Error: {str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TOTPGenerator()
    window.show()
    sys.exit(app.exec_())