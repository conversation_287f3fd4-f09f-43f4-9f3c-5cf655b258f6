#!/usr/bin/env python3
"""
测试密钥验证功能
"""
import sys
import re
import base64
import pyotp

def normalize_secret_key(secret):
    """
    标准化和修复TOTP密钥格式
    """
    # 移除所有空格和常见分隔符
    secret = re.sub(r'[\s\-_]', '', secret.upper())
    
    # 如果密钥以"Key "开头，移除它
    if secret.startswith('KEY'):
        secret = secret[3:]
    
    # 确保密钥只包含有效的Base32字符
    # Base32字符集: A-Z, 2-7
    secret = re.sub(r'[^A-Z2-7]', '', secret)
    
    # 修复Base32 padding
    # Base32需要长度是8的倍数，用'='填充
    while len(secret) % 8 != 0:
        secret += '='
        
    return secret

def validate_secret_key(secret):
    """
    验证密钥格式是否正确
    """
    try:
        normalized = normalize_secret_key(secret)
        if len(normalized) < 16:  # 最小长度检查
            return False, "Secret key too short (minimum 16 characters)"
        
        # 尝试解码Base32
        base64.b32decode(normalized)
        return True, normalized
    except Exception as e:
        return False, f"Invalid Base32 format: {str(e)}"

def test_key_formats():
    """
    测试各种密钥格式
    """
    test_keys = [
        "JBSWY3DPEHPK3PXP",  # 标准Base32
        "Key JBSWY3DPEHPK3PXP",  # 带Key前缀
        "JBSW Y3DP EHPK 3PXP",  # 带空格
        "JBSW-Y3DP-EHPK-3PXP",  # 带连字符
        "jbswy3dpehpk3pxp",  # 小写
        "Key sdvb1234",  # 用户提到的格式
        "INVALID123!@#",  # 无效字符
        "SHORT",  # 太短
        "",  # 空字符串
    ]
    
    print("🧪 Testing Key Validation Function")
    print("=" * 50)
    
    for i, key in enumerate(test_keys, 1):
        print(f"\n{i}. Testing: '{key}'")
        is_valid, result = validate_secret_key(key)
        
        if is_valid:
            print(f"   ✅ Valid - Normalized: '{result}'")
            # 尝试生成TOTP代码
            try:
                totp = pyotp.TOTP(result)
                code = totp.now()
                print(f"   🔢 Generated code: {code}")
            except Exception as e:
                print(f"   ❌ TOTP generation failed: {e}")
        else:
            print(f"   ❌ Invalid - Error: {result}")

if __name__ == "__main__":
    test_key_formats()
